import { Layout } from "../components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { useOrganizations } from "../hooks/useOrganizations";
import { useServices } from "../hooks/useServices";
import { Link } from "wouter";
import {
  Calendar,
  Users,
  Settings,
  PlusCircle,
  TrendingUp,
  Clock,
  Home,
  CheckCircle,
  BarChart3,
  Activity,
  ArrowRight,
  Sparkles
} from "lucide-react";
import { formatCurrency } from "../utils/formatters";

export default function HomePage() {
  const { organizations, isLoading: organizationsLoading } = useOrganizations();
  const { services, isLoading: servicesLoading } = useServices();

  const stats = {
    totalOrganizations: organizations?.length || 0,
    totalServices: services?.length || 0,
    totalRevenue: organizations?.reduce((sum, org) => sum + (org.totalAmount || 0), 0) || 0,
    confirmedOrganizations: organizations?.filter(org => org.status === 'Kesinleşti').length || 0,
    thisMonthEvents: organizations?.filter(org => {
      const eventDate = new Date(org.eventDate);
      const now = new Date();
      return eventDate.getMonth() === now.getMonth() &&
             eventDate.getFullYear() === now.getFullYear();
    }).length || 0,
    activeServices: services?.filter(service => service.isActive).length || 0,
  };

  const recentOrganizations = organizations
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5);

  return (
    <Layout
      title="Ana Sayfa"
      subtitle="Organizasyon Defteri yönetim sistemi"
    >
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8 space-y-6 sm:space-y-8">
          {/* Header Section */}
          <div className="text-center space-y-3 sm:space-y-4">
            <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg mb-2 sm:mb-4">
              <Home className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 px-2">Organizasyon Defteri</h1>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto px-4">
              Profesyonel organizasyon yönetimi için modern ve kullanıcı dostu platform
            </p>
          </div>

          {/* Welcome Banner */}
          <div className="bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-600 rounded-xl sm:rounded-2xl p-4 sm:p-6 lg:p-8 text-white shadow-xl relative overflow-hidden">
            <div className="absolute top-0 right-0 w-20 h-20 sm:w-32 sm:h-32 bg-white/10 rounded-full -translate-y-10 translate-x-10 sm:-translate-y-16 sm:translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-16 h-16 sm:w-24 sm:h-24 bg-white/10 rounded-full translate-y-8 -translate-x-8 sm:translate-y-12 sm:-translate-x-12"></div>
            <div className="relative z-10">
              <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                <Sparkles className="w-6 h-6 sm:w-8 sm:h-8 text-yellow-300 flex-shrink-0" />
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold">Hoş Geldiniz!</h2>
              </div>
              <p className="text-blue-100 text-sm sm:text-base lg:text-lg mb-4 sm:mb-6 max-w-2xl">
                Organizasyonlarınızı profesyonelce yönetin, hizmetlerinizi düzenleyin ve işinizi büyütün
              </p>
              <div className="flex flex-col gap-3 sm:gap-4">
                <Link href="/new-organization" className="w-full">
                  <Button size="lg" className="w-full bg-white text-blue-600 hover:bg-gray-100 font-semibold">
                    <PlusCircle className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                    Yeni Organizasyon
                  </Button>
                </Link>
                <Link href="/organizations" className="w-full">
                  <Button size="lg" className="w-full bg-white/20 backdrop-blur-sm border-2 border-white text-white font-semibold hover:bg-white hover:text-blue-600 transition-all duration-200 shadow-lg">
                    <Users className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-white group-hover:text-blue-600" />
                    Organizasyonları Görüntüle
                    <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4 ml-2 text-white group-hover:text-blue-600" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Toplam Organizasyon</p>
                  <p className="text-3xl font-bold text-gray-900">
                    {organizationsLoading ? "..." : stats.totalOrganizations}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">Bu ay {stats.thisMonthEvents} etkinlik</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Kesinleşen</p>
                  <p className="text-3xl font-bold text-green-600">
                    {organizationsLoading ? "..." : stats.confirmedOrganizations}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">Onaylanmış organizasyon</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Aktif Hizmet</p>
                  <p className="text-3xl font-bold text-purple-600">
                    {servicesLoading ? "..." : stats.activeServices}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">{stats.totalServices} toplam hizmet</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <Settings className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Toplam Hasılat</p>
                  <p className="text-3xl font-bold text-orange-600">
                    {organizationsLoading ? "..." : formatCurrency(stats.totalRevenue)}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">Tüm organizasyonlar</p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions & Recent Organizations */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Quick Actions */}
            <div className="bg-white rounded-2xl p-6 shadow-lg border-0">
              <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <Activity className="w-6 h-6 mr-3 text-blue-600" />
                Hızlı Eylemler
              </h3>
              <div className="space-y-4">
                <Link href="/new-organization">
                  <Button size="lg" className="w-full justify-start bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-200 h-14">
                    <PlusCircle className="mr-3 h-5 w-5" />
                    <div className="text-left">
                      <div className="font-semibold">Yeni Organizasyon</div>
                      <div className="text-xs text-blue-100">Organizasyon oluştur</div>
                    </div>
                  </Button>
                </Link>
                <Link href="/services">
                  <Button variant="outline" size="lg" className="w-full justify-start border-2 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 h-14">
                    <Settings className="mr-3 h-5 w-5" />
                    <div className="text-left">
                      <div className="font-semibold">Hizmet Yönetimi</div>
                      <div className="text-xs text-gray-500">Hizmetleri düzenle</div>
                    </div>
                  </Button>
                </Link>
                <Link href="/organizations">
                  <Button variant="outline" size="lg" className="w-full justify-start border-2 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 h-14">
                    <Users className="mr-3 h-5 w-5" />
                    <div className="text-left">
                      <div className="font-semibold">Organizasyonlar</div>
                      <div className="text-xs text-gray-500">Tüm organizasyonları görüntüle</div>
                    </div>
                  </Button>
                </Link>
              </div>
            </div>

            {/* Recent Organizations */}
            <div className="bg-white rounded-2xl p-6 shadow-lg border-0">
              <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <BarChart3 className="w-6 h-6 mr-3 text-green-600" />
                Son Organizasyonlar
              </h3>
              {organizationsLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="h-20 bg-gray-100 rounded-xl animate-pulse" />
                  ))}
                </div>
              ) : recentOrganizations.length > 0 ? (
                <div className="space-y-4">
                  {recentOrganizations.map((org) => (
                    <div key={org.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-all duration-200">
                      <div className="flex-1">
                        <p className="font-semibold text-gray-900">{org.customerName}</p>
                        <p className="text-sm text-gray-600 mt-1">
                          {new Date(org.eventDate).toLocaleDateString('tr-TR')} • {org.services?.length || 0} hizmet
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-lg text-gray-900">{formatCurrency(org.totalAmount || 0)}</p>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          org.status === 'Kesinleşti' ? 'bg-green-100 text-green-800' :
                          org.status === 'İptal' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {org.status}
                        </span>
                      </div>
                    </div>
                  ))}
                  <Link href="/organizations">
                    <Button variant="outline" className="w-full mt-4 border-2 hover:bg-gray-50">
                      Tüm Organizasyonları Görüntüle
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Calendar className="w-8 h-8 text-gray-400" />
                  </div>
                  <p className="text-gray-500 font-medium">Henüz organizasyon bulunmuyor</p>
                  <p className="text-gray-400 text-sm mt-2 mb-6">İlk organizasyonunuzu oluşturun</p>
                  <Link href="/new-organization">
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      <PlusCircle className="w-4 h-4 mr-2" />
                      Yeni Organizasyon
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
