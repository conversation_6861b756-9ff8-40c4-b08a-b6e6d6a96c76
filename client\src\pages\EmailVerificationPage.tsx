import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Mail, Loader2, ArrowRight, Shield } from 'lucide-react';
import { useLocation } from 'wouter';
import { supabase } from '../lib/supabase';

export default function EmailVerificationPage() {
  const [, setLocation] = useLocation();
  const [step, setStep] = useState<'verifying' | 'success' | 'error'>('verifying');
  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    const processEmailVerification = async () => {
      try {
        console.log('📧 Processing email verification...');

        // Check URL parameters for verification tokens
        const urlParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(window.location.hash.substring(1));

        const accessToken = urlParams.get('access_token') || hashParams.get('access_token');
        const refreshToken = urlParams.get('refresh_token') || hashParams.get('refresh_token');

        if (accessToken && refreshToken) {
          console.log('🔑 Found auth tokens, setting session...');

          // Set the session with the tokens
          const { data, error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          });

          if (error) {
            console.error('❌ Error setting session:', error);
            setStep('error');
            return;
          }

          if (data.user) {
            console.log('✅ Session set successfully, user:', data.user.email);

            // Create profile if needed
            const userMetadata = data.user.user_metadata;
            if (userMetadata && userMetadata.first_name) {
              console.log('📝 Creating user profile...');

              try {
                // Check if profile exists
                const { data: existingProfile } = await supabase
                  .from('eventflow_profiles')
                  .select('id')
                  .eq('id', data.user.id)
                  .single();

                if (!existingProfile) {
                  // Create profile
                  await supabase
                    .from('eventflow_profiles')
                    .insert({
                      id: data.user.id,
                      first_name: userMetadata.first_name,
                      last_name: userMetadata.last_name,
                      company_name: userMetadata.company_name,
                      phone: userMetadata.phone || null,
                      is_active: true,
                      created_at: new Date().toISOString(),
                      updated_at: new Date().toISOString()
                    });

                  console.log('✅ Profile created successfully');
                }
              } catch (profileError) {
                console.error('❌ Error creating profile:', profileError);
              }
            }

            // Sign out and show success
            await supabase.auth.signOut();
          }
        }

        // Show success after processing
        setTimeout(() => {
          setStep('success');

          // Start countdown for redirect
          const countdownTimer = setInterval(() => {
            setCountdown(prev => {
              if (prev <= 1) {
                clearInterval(countdownTimer);
                setLocation('/auth');
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        }, 2000);

      } catch (error) {
        console.error('❌ Email verification error:', error);
        setStep('error');
      }
    };

    processEmailVerification();
  }, [setLocation]);

  const handleManualRedirect = () => {
    setLocation('/auth');
  };

  if (step === 'verifying') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center space-y-6 pb-8">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto relative">
              <Mail className="w-10 h-10 text-white" />
              <div className="absolute inset-0 rounded-full border-4 border-blue-200 animate-pulse"></div>
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                E-posta Adresiniz Doğrulanıyor
              </CardTitle>
              <p className="text-gray-600">
                Lütfen bekleyin, üyeliğiniz aktif hale getiriliyor...
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <Loader2 className="w-5 h-5 text-blue-600 animate-spin flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-blue-900 mb-1">
                    Doğrulama İşlemi Devam Ediyor
                  </p>
                  <p className="text-blue-700">
                    E-posta adresiniz kontrol ediliyor ve hesabınız aktif hale getiriliyor.
                    Bu işlem birkaç saniye sürebilir.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-4 text-white">
              <div className="flex items-center space-x-3">
                <Shield className="w-5 h-5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium mb-1">
                    Güvenli Doğrulama
                  </p>
                  <p className="text-blue-100">
                    Hesabınızın güvenliği için e-posta doğrulaması yapılıyor.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (step === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center space-y-6 pb-8">
            <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto relative">
              <CheckCircle className="w-10 h-10 text-white" />
              <div className="absolute inset-0 rounded-full border-4 border-green-200 animate-pulse"></div>
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-green-600 mb-2">
                E-mail Bilgileriniz Doğrulandı! 🎉
              </CardTitle>
              <p className="text-gray-600">
                Hesabınız başarıyla aktif hale getirildi
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-green-900 mb-1">
                    Doğrulama Başarılı!
                  </p>
                  <p className="text-green-700">
                    E-posta adresiniz doğrulandı ve hesabınız aktif hale getirildi. 
                    Artık sisteme giriş yapabilirsiniz.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div className="text-sm">
                  <p className="font-medium mb-1">
                    Giriş sayfasına yönlendiriliyorsunuz...
                  </p>
                  <p className="text-green-100">
                    {countdown} saniye içinde otomatik yönlendirme
                  </p>
                </div>
                <div className="text-2xl font-bold">
                  {countdown}
                </div>
              </div>
            </div>

            <Button
              onClick={handleManualRedirect}
              className="w-full h-12 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <span>Hemen Giriş Sayfasına Git</span>
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
}
