import { useEffect } from "react";
import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { HelmetProvider } from "react-helmet-async";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "./contexts/AuthContext";
import { initializeViewport, optimizeMobileScroll, cleanupViewport } from "./utils/viewport";
import { ProtectedRoute } from "./components/auth/ProtectedRoute";
import { AdminProtectedRoute } from "./components/admin/AdminProtectedRoute";
import { DynamicMetaTags } from "./components/DynamicMetaTags";
import NotFound from "@/pages/not-found";
import HomePage from "@/pages/HomePage";
import ServicesPage from "@/pages/ServicesPage";
import NewOrganizationPage from "@/pages/NewOrganizationPage";
import AgendaPage from "@/pages/AgendaPage";
import OrganizationsPage from "@/pages/OrganizationsPage";
import SettingsPage from "@/pages/SettingsPage";
import AdminDashboard from "@/pages/admin/AdminDashboard";
import AdminUsersPage from "@/pages/admin/AdminUsersPage";
import AdminStatsPage from "@/pages/admin/AdminStatsPage";
import AdminSettingsPage from "@/pages/admin/AdminSettingsPage";
import AdminLogsPage from "@/pages/admin/AdminLogsPage";
import AdminActivitiesPage from "@/pages/admin/AdminActivitiesPage";
import AdminManagementPage from "@/pages/admin/AdminManagementPage";
import AdminNotificationsPage from "@/pages/admin/AdminNotificationsPage";
import AdminSecurityPage from "@/pages/admin/AdminSecurityPage";
import AdminReportsPage from "@/pages/admin/AdminReportsPage";
import EmailConfirmationPage from "@/pages/EmailConfirmationPage";
import EmailVerifiedPage from "@/pages/EmailVerifiedPage";

function Router() {
  return (
    <Switch>
      {/* Public Routes */}
      <Route path="/email-confirmation" component={EmailConfirmationPage} />
      <Route path="/email-verified" component={EmailVerifiedPage} />

      {/* Admin Routes - Flat routing */}
      <Route path="/admin">
        <AdminProtectedRoute>
          <AdminDashboard />
        </AdminProtectedRoute>
      </Route>

      <Route path="/admin/users">
        <AdminProtectedRoute>
          <AdminUsersPage />
        </AdminProtectedRoute>
      </Route>

      <Route path="/admin/stats">
        <AdminProtectedRoute>
          <AdminStatsPage />
        </AdminProtectedRoute>
      </Route>

      <Route path="/admin/settings">
        <AdminProtectedRoute requiredRole="super_admin">
          <AdminSettingsPage />
        </AdminProtectedRoute>
      </Route>

      <Route path="/admin/logs">
        <AdminProtectedRoute>
          <AdminLogsPage />
        </AdminProtectedRoute>
      </Route>

      <Route path="/admin/activities">
        <AdminProtectedRoute>
          <AdminActivitiesPage />
        </AdminProtectedRoute>
      </Route>

      <Route path="/admin/admins">
        <AdminProtectedRoute requiredRole="super_admin">
          <AdminManagementPage />
        </AdminProtectedRoute>
      </Route>

      <Route path="/admin/notifications">
        <AdminProtectedRoute>
          <AdminNotificationsPage />
        </AdminProtectedRoute>
      </Route>

      <Route path="/admin/security">
        <AdminProtectedRoute>
          <AdminSecurityPage />
        </AdminProtectedRoute>
      </Route>

      <Route path="/admin/reports">
        <AdminProtectedRoute>
          <AdminReportsPage />
        </AdminProtectedRoute>
      </Route>

      {/* Regular App Routes */}
      <Route>
        <ProtectedRoute>
          <Switch>
            <Route path="/" component={HomePage} />
            <Route path="/services" component={ServicesPage} />
            <Route path="/new-organization" component={NewOrganizationPage} />
            <Route path="/calendar" component={AgendaPage} />
            <Route path="/organizations" component={OrganizationsPage} />
            <Route path="/settings" component={SettingsPage} />
            <Route component={NotFound} />
          </Switch>
        </ProtectedRoute>
      </Route>
    </Switch>
  );
}

function App() {
  useEffect(() => {
    // Initialize viewport handling for mobile optimization
    initializeViewport();
    optimizeMobileScroll();

    // Cleanup on unmount
    return () => {
      cleanupViewport();
    };
  }, []);

  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <TooltipProvider>
            <DynamicMetaTags />
            <Toaster />
            <Router />
          </TooltipProvider>
        </AuthProvider>
      </QueryClientProvider>
    </HelmetProvider>
  );
}

export default App;
