import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase, sessionHelpers } from '../lib/supabase'
import { ActivityLogger } from '../lib/activityLogger'
import { securityHelper } from '../lib/securityHelper'

interface UserProfile {
  id: string
  firstName: string
  lastName: string
  companyName: string
  phone: string | null
  createdAt: string
  updatedAt: string
}

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  session: Session | null
  loading: boolean
  emailConfirmationSent: boolean
  signUp: (email: string, password: string, userData: {
    firstName: string
    lastName: string
    companyName: string
    phone?: string
  }) => Promise<{ error: AuthError | null; needsConfirmation?: boolean }>
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  resendConfirmation: (email: string) => Promise<{ error: AuthError | null }>
  updateProfile: (updates: Partial<Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>>) => Promise<{ error: Error | null }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [emailConfirmationSent, setEmailConfirmationSent] = useState(false)

  useEffect(() => {
    console.log('🔄 AuthProvider initializing...')
    let mounted = true

    // Get initial session with mobile-specific handling
    const initializeAuth = async () => {
      try {
        console.log('📡 Getting initial session...')

        // Simple session fetch without timeout complexity
        const { data: { session }, error } = await supabase.auth.getSession()

        if (!mounted) {
          console.log('❌ Component unmounted, aborting')
          return
        }

        if (error) {
          console.error('❌ Session error:', error)

          // Don't fail completely on session errors, continue with null session
          console.log('⚠️ Continuing with null session due to error')
          session = null;
        }

        console.log('✅ Session received:', session?.user?.id ? 'User found' : 'No user')
        setSession(session)
        setUser(session?.user ?? null)

        // Loading'i hemen false yap - user set edildi
        console.log('✅ Auth initialization complete - setting loading false')
        setLoading(false)

        if (session?.user) {
          console.log('👤 Fetching profile in background during init for user:', session.user.id)
          console.log('👤 User email:', session.user.email)
          // Profile fetch'i background'da yap - blocking yapmayalım
          fetchProfile(session.user.id).catch(profileError => {
            console.warn('⚠️ Background profile fetch failed during init:', profileError)
            setProfile(null)
          })

          // NOT: ensureUserProfile çağrısını kaldırıyoruz çünkü fetchProfile zaten gerektiğinde profil oluşturacak
        }

      } catch (error) {
        console.error('❌ Auth initialization error:', error)
        if (mounted) {
          // Don't fail completely, set safe defaults
          setSession(null)
          setUser(null)
          setProfile(null)
          setLoading(false)
        }
      }
    }

    initializeAuth()

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return

      console.log('🔄 Auth state change:', event, session?.user?.id)

      setSession(session)
      setUser(session?.user ?? null)

      // Loading'i hemen false yap - user set edildi
      setLoading(false)

      if (session?.user) {
        console.log('👤 User authenticated:', session.user.email)

        let profileCreated = false

        // Check if this is a newly confirmed user from email verification
        if (event === 'SIGNED_IN' && session.user.email_confirmed_at) {
          // Check if this is coming from email verification link
          const isFromEmailVerification = window.location.hash.includes('type=email') ||
                                         window.location.search.includes('type=email') ||
                                         sessionStorage.getItem('pendingUserData');

          if (isFromEmailVerification) {
            console.log('📧 Email verification detected, redirecting to verification page')
            // Don't create profile here, redirect to EmailVerificationPage instead
            setEmailConfirmationSent(false)

            // Store user data for later use
            const userMetadata = session.user.user_metadata
            if (userMetadata && userMetadata.first_name) {
              sessionStorage.setItem('verifiedUserData', JSON.stringify({
                userId: session.user.id,
                firstName: userMetadata.first_name,
                lastName: userMetadata.last_name,
                companyName: userMetadata.company_name,
                phone: userMetadata.phone
              }))
            }

            // Sign out and redirect to verification page
            await supabase.auth.signOut()
            window.location.href = '/email-verification'
            return
          }

          // Normal sign in - create profile if needed
          const userMetadata = session.user.user_metadata
          if (userMetadata && userMetadata.first_name) {
            try {
              const userData = {
                firstName: userMetadata.first_name,
                lastName: userMetadata.last_name,
                companyName: userMetadata.company_name,
                phone: userMetadata.phone
              }
              console.log('📝 Creating profile for signed in user')
              await createUserProfile(session.user.id, userData)
              profileCreated = true
            } catch (error) {
              console.error('❌ Error creating profile for signed in user:', error)
            }
          }
        }

        // Only fetch profile in background if we didn't just create it
        if (!profileCreated) {
          console.log('👤 Fetching profile in background')
          // Profile'ı background'da fetch et - blocking yapmayalım
          fetchProfile(session.user.id).catch(profileError => {
            console.warn('⚠️ Background profile fetch failed:', profileError)
            setProfile(null)
          })
        }
      } else {
        setProfile(null)
      }
    })

    return () => {
      console.log('🧹 AuthProvider cleanup')
      mounted = false
      subscription.unsubscribe()
    }
  }, [])

  const fetchProfile = async (userId: string) => {
    console.log('📋 Fetching profile for user:', userId)

    try {
      // Simple, direct query - no mobile/desktop complexity
      const { data, error } = await supabase
        .from('eventflow_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('❌ Profile fetch error:', error);
        setProfile(null)
        return
      }

      if (!data) {
        console.log('ℹ️ No profile data returned')
        setProfile(null)
        return
      }

      console.log('✅ Profile fetched successfully:', data)
      console.log('🔍 Raw profile data:', {
        first_name: data.first_name,
        last_name: data.last_name,
        company_name: data.company_name
      })

      // Convert snake_case to camelCase
      const profileData: UserProfile = {
        id: data.id,
        firstName: data.first_name || '',
        lastName: data.last_name || '',
        companyName: data.company_name || '',
        phone: data.phone,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      }

      console.log('🔄 Converted profile data:', {
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        companyName: profileData.companyName
      })

      setProfile(profileData)

    } catch (error) {
      console.warn('⚠️ Profile fetch failed (continuing anyway):', error)
      setProfile(null)
    }
  }

  const signUp = async (email: string, password: string, userData: {
    firstName: string
    lastName: string
    companyName: string
    phone?: string
  }) => {
    try {
      console.log('📝 Starting sign up process for:', email)

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: userData.firstName,
            last_name: userData.lastName,
            company_name: userData.companyName,
            phone: userData.phone || null,
          }
        }
      })

      if (error) {
        console.error('❌ Sign up error:', error)

        // Translate common error messages to Turkish
        let turkishMessage = error.message;

        if (error.message.includes('User already registered')) {
          turkishMessage = 'Bu email adresi ile zaten kayıtlı bir hesap bulunuyor.';
        } else if (error.message.includes('Password should be at least')) {
          turkishMessage = 'Şifre en az 6 karakter olmalıdır.';
        } else if (error.message.includes('Invalid email')) {
          turkishMessage = 'Geçersiz email adresi formatı.';
        } else if (error.message.includes('Signup is disabled')) {
          turkishMessage = 'Yeni kayıtlar şu anda kapalıdır.';
        }

        return { error: { ...error, message: turkishMessage } }
      }

      console.log('✅ Sign up successful:', data)

      // Check if email confirmation is needed
      if (data.user && !data.user.email_confirmed_at) {
        console.log('📧 Email confirmation required - user data stored in auth metadata')
        setEmailConfirmationSent(true)

        // User data is now stored in auth metadata, no need for sessionStorage
        // Keep sessionStorage as fallback for existing users
        sessionStorage.setItem('pendingUserData', JSON.stringify({
          userId: data.user.id,
          ...userData
        }))

        return { error: null, needsConfirmation: true }
      }

      // If user is already confirmed (shouldn't happen with new setup)
      if (data.user && data.user.email_confirmed_at) {
        console.log('✅ User already confirmed, creating profile')
        await createUserProfile(data.user.id, userData)
      }

      return { error: null, needsConfirmation: false }
    } catch (error) {
      console.error('❌ Sign up error:', error)
      return { error: error as AuthError }
    }
  }

  // Helper function to ensure user profile exists (for mobile users)
  const ensureUserProfile = async (userId: string, email: string) => {
    try {
      console.log('🔍 Ensuring profile exists for user:', userId)

      // Try multiple methods to check if profile exists (to avoid 406 errors)
      let existingProfile = null;

      try {
        // Method 1: Try maybeSingle
        const { data: profile1 } = await supabase
          .from('eventflow_profiles')
          .select('id')
          .eq('id', userId)
          .maybeSingle();
        existingProfile = profile1;
      } catch (error1) {
        console.warn('⚠️ Method 1 failed, trying alternative...', error1);

        try {
          // Method 2: Try with limit
          const { data: profile2 } = await supabase
            .from('eventflow_profiles')
            .select('id')
            .eq('id', userId)
            .limit(1);
          existingProfile = profile2 && profile2.length > 0 ? profile2[0] : null;
        } catch (error2) {
          console.warn('⚠️ Method 2 also failed, assuming no profile exists...', error2);
          existingProfile = null;
        }
      }

      if (existingProfile) {
        console.log('✅ Profile already exists for user:', userId);
        return;
      }

      console.log('📱 No profile found, creating basic profile for mobile user...');

      // Create a basic profile for mobile users
      const { data, error: profileError } = await supabase
        .from('eventflow_profiles')
        .insert({
          id: userId,
          first_name: 'Kullanıcı',
          last_name: '',
          company_name: 'Şirket Adı',
          phone: null,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (profileError) {
        // If error is duplicate key, it means profile already exists
        if (profileError.code === '23505') {
          console.log('✅ Profile already exists (duplicate key error), skipping creation');
          return;
        }
        console.error('❌ Error creating basic profile:', profileError)
        return;
      }

      console.log('✅ Basic profile created for mobile user:', data)

      // Refresh profile after creation
      await fetchProfile(userId);

    } catch (error) {
      console.error('❌ Failed to ensure user profile:', error)
    }
  }

  // Helper function to create user profile
  const createUserProfile = async (userId: string, userData: {
    firstName: string
    lastName: string
    companyName: string
    phone?: string
  }) => {
    try {
      console.log('👤 Creating user profile for:', userId)
      console.log('📝 User data:', userData)

      // First check if profile already exists (use safe method)
      let existingProfile = null;
      try {
        console.log('🔍 Checking if profile exists...')
        const { data: profileCheck, error: checkError } = await supabase
          .from('eventflow_profiles')
          .select('id')
          .eq('id', userId)
          .limit(1);

        if (checkError) {
          console.error('❌ Profile check error:', checkError);
          throw checkError;
        }

        existingProfile = profileCheck && profileCheck.length > 0 ? profileCheck[0] : null;
        console.log('🔍 Profile check result:', existingProfile ? 'EXISTS' : 'NOT EXISTS');
      } catch (checkError) {
        console.error('❌ Profile check failed:', checkError);
        existingProfile = null;
      }

      if (existingProfile) {
        console.log('ℹ️ Profile already exists for user:', userId);
        console.log('📋 Fetching existing profile...');
        // Profile exists, just fetch and set it
        await fetchProfile(userId);
        console.log('✅ Profile fetch completed');
        return;
      }

      console.log('📝 Creating new profile...')
      const { data, error: profileError } = await supabase
        .from('eventflow_profiles')
        .insert({
          id: userId,
          first_name: userData.firstName,
          last_name: userData.lastName,
          company_name: userData.companyName,
          phone: userData.phone || null,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .limit(1);

      if (profileError) {
        console.error('❌ Error creating profile:', profileError)
        throw profileError
      }

      // Handle data from limit(1) query
      const profileData = data && data.length > 0 ? data[0] : data;
      console.log('✅ Profile created successfully:', profileData)

      console.log('📋 Fetching newly created profile...')
      // Immediately fetch and set the profile
      await fetchProfile(userId);
      console.log('✅ Profile fetch completed after creation')

      // Log user activity with detailed information - only after profile is created
      const { data: { user } } = await supabase.auth.getUser();
      try {
        await ActivityLogger.register({
          email: user?.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          companyName: userData.companyName
        });
      } catch (activityError) {
        console.warn('⚠️ Failed to log registration activity (non-critical):', activityError);
      }
    } catch (error) {
      console.error('❌ Profile creation failed:', error)
      throw error
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      console.log('🔐 Attempting sign in for:', email)

      // Check if email is locked out (localStorage)
      const emailLockout = securityHelper.isEmailLockedOut(email);
      if (emailLockout.locked) {
        const message = `Bu hesap ${emailLockout.remainingTime} dakika boyunca kilitli. Çok fazla başarısız giriş denemesi yapıldı.`;
        return { error: { message } as AuthError };
      }

      // Check database for active locks (only for this user)
      try {
        const { data: dbLocks } = await supabase
          .from('locked_accounts')
          .select('*')
          .eq('email', email.toLowerCase())
          .eq('is_active', true)
          .limit(1);

        if (dbLocks && dbLocks.length > 0) {
          const lock = dbLocks[0];
          const lockEndTime = new Date(lock.locked_until).getTime();
          const now = Date.now();

          // If lock has expired, clean it automatically
          if (lockEndTime <= now) {
            console.log('🧹 Cleaning expired lock for user:', email);
            await supabase
              .from('locked_accounts')
              .update({ is_active: false, updated_at: new Date().toISOString() })
              .eq('email', email.toLowerCase());

            console.log('✅ Expired lock cleaned for user:', email);
          } else {
            // Lock is still active
            const remainingTime = Math.ceil((lockEndTime - now) / 1000 / 60);
            const message = `Bu hesap ${Math.max(0, remainingTime)} dakika boyunca kilitli. Çok fazla başarısız giriş denemesi yapıldı.`;
            return { error: { message } as AuthError };
          }
        }
      } catch (error) {
        console.error('Error checking database locks:', error);
      }

      // Check if IP is rate limited (localStorage)
      const ipRateLimit = await securityHelper.isIPRateLimited();
      if (ipRateLimit.limited) {
        const message = `Bu IP adresi ${ipRateLimit.remainingTime} dakika boyunca kısıtlı. Çok fazla giriş denemesi yapıldı.`;
        return { error: { message } as AuthError };
      }

      // Check database for IP rate limits (only for this IP)
      try {
        const userIP = await securityHelper.getUserIP();
        const { data: ipLimits } = await supabase
          .from('rate_limited_ips')
          .select('*')
          .eq('ip_address', userIP)
          .eq('is_active', true)
          .limit(1);

        if (ipLimits && ipLimits.length > 0) {
          const limit = ipLimits[0];
          const limitEndTime = new Date(limit.limited_until).getTime();
          const now = Date.now();

          // If limit has expired, clean it automatically
          if (limitEndTime <= now) {
            console.log('🧹 Cleaning expired IP limit for:', userIP);
            await supabase
              .from('rate_limited_ips')
              .update({ is_active: false, updated_at: new Date().toISOString() })
              .eq('ip_address', userIP);

            console.log('✅ Expired IP limit cleaned for:', userIP);
          } else {
            // Limit is still active
            const remainingTime = Math.ceil((limitEndTime - now) / 1000 / 60);
            const message = `Bu IP adresi ${Math.max(0, remainingTime)} dakika boyunca kısıtlı. Çok fazla giriş denemesi yapıldı.`;
            return { error: { message } as AuthError };
          }
        }
      } catch (error) {
        console.error('Error checking IP rate limits:', error);
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error('❌ Sign in error:', error)

        // Record failed login attempt
        await securityHelper.recordLoginAttempt(email, false);

        // Get remaining attempts for user feedback
        const remainingAttempts = securityHelper.getRemainingAttempts(email);

        // Translate common error messages to Turkish
        let turkishMessage = error.message;

        if (error.message.includes('Invalid login credentials')) {
          turkishMessage = `Email adresi veya şifre hatalı. ${remainingAttempts} deneme hakkınız kaldı.`;
        } else if (error.message.includes('Email not confirmed')) {
          turkishMessage = 'Email adresinizi doğrulamanız gerekiyor. Lütfen email kutunuzu kontrol edin.';
          setEmailConfirmationSent(true);
        } else if (error.message.includes('Too many requests')) {
          turkishMessage = 'Çok fazla deneme yaptınız. Lütfen bir süre bekleyip tekrar deneyin.';
        } else if (error.message.includes('User not found')) {
          turkishMessage = 'Bu email adresi ile kayıtlı kullanıcı bulunamadı.';
        } else if (error.message.includes('Invalid email')) {
          turkishMessage = 'Geçersiz email adresi formatı.';
        }

        // Add remaining attempts info if credentials are wrong
        if (remainingAttempts <= 2 && remainingAttempts > 0) {
          turkishMessage += ` Dikkat: ${remainingAttempts} başarısız denemeden sonra hesabınız kilitlenecek.`;
        }

        return { error: { ...error, message: turkishMessage } }
      }

      // Check if user has confirmed email
      if (data.user && !data.user.email_confirmed_at) {
        console.log('📧 Email not confirmed yet')
        setEmailConfirmationSent(true)
        return { error: { message: 'Email adresinizi doğrulamanız gerekiyor. Lütfen email kutunuzu kontrol edin.' } as AuthError }
      }

      console.log('✅ Sign in successful')

      // Record successful login attempt
      await securityHelper.recordLoginAttempt(email, true);

      // Log user activity (non-blocking)
      try {
        ActivityLogger.login()
      } catch (activityError) {
        console.warn('⚠️ Failed to log login activity (non-critical):', activityError);
      }

      return { error: null }
    } catch (error) {
      console.error('❌ Sign in error:', error)
      return { error: error as AuthError }
    }
  }

  const resendConfirmation = async (email: string) => {
    try {
      console.log('📧 Resending confirmation email to:', email)

      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      })

      if (error) {
        console.error('❌ Resend confirmation error:', error)
        return { error }
      }

      console.log('✅ Confirmation email resent')
      return { error: null }
    } catch (error) {
      console.error('❌ Resend confirmation error:', error)
      return { error: error as AuthError }
    }
  }

  const signOut = async () => {
    try {
      // Log user activity before signing out
      ActivityLogger.logout()

      const { error } = await supabase.auth.signOut()

      // Clear session storage
      sessionHelpers.clearSession()

      return { error }
    } catch (error) {
      console.error('Sign out error:', error)
      return { error: error as AuthError }
    }
  }

  const updateProfile = async (updates: Partial<Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>>) => {
    if (!user) {
      return { error: new Error('No user logged in') }
    }

    try {
      // Convert camelCase to snake_case for database
      const dbUpdates: any = {}
      if (updates.firstName) dbUpdates.first_name = updates.firstName
      if (updates.lastName) dbUpdates.last_name = updates.lastName
      if (updates.companyName) dbUpdates.company_name = updates.companyName
      if (updates.phone !== undefined) dbUpdates.phone = updates.phone

      const { error } = await supabase
        .from('eventflow_profiles')
        .update({
          ...dbUpdates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id)

      if (error) {
        return { error }
      }

      // Refresh profile
      await fetchProfile(user.id)

      // Log user activity
      ActivityLogger.updateProfile(updates)

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  }

  const value = {
    user,
    profile,
    session,
    loading,
    emailConfirmationSent,
    signUp,
    signIn,
    signOut,
    resendConfirmation,
    updateProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
