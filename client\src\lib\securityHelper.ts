import { createSystemNotification } from './activityLogger';
import { supabase } from './supabase';

interface LoginAttempt {
  email: string;
  timestamp: number;
  success: boolean;
  ip?: string;
}

interface SecurityLimits {
  maxAttemptsPerEmail: number;
  maxAttemptsPerIP: number;
  lockoutDurationMinutes: number;
  rateLimitWindowMinutes: number;
}

const DEFAULT_LIMITS: SecurityLimits = {
  maxAttemptsPerEmail: 5,        // Email başına maksimum 5 deneme
  maxAttemptsPerIP: 5,           // IP başına maksimum 5 deneme (test için düşürüldü)
  lockoutDurationMinutes: 15,    // 15 dakika hesap kilidi
  rateLimitWindowMinutes: 15     // 15 dakikalık zaman penceresi
};

class SecurityHelper {
  private storageKey = 'login_attempts';
  private limits: SecurityLimits;

  constructor(limits: SecurityLimits = DEFAULT_LIMITS) {
    this.limits = limits;
  }

  /**
   * Get user's IP address (simplified for demo)
   */
  private async getUserIP(): Promise<string> {
    try {
      // In production, you might want to use a more reliable IP detection service
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip || 'unknown';
    } catch {
      return 'unknown';
    }
  }

  /**
   * Get stored login attempts
   */
  private getStoredAttempts(): LoginAttempt[] {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  /**
   * Store login attempts
   */
  private storeAttempts(attempts: LoginAttempt[]): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(attempts));
    } catch (error) {
      console.error('Failed to store login attempts:', error);
    }
  }

  /**
   * Clean old attempts outside the rate limit window
   */
  private cleanOldAttempts(attempts: LoginAttempt[]): LoginAttempt[] {
    const cutoffTime = Date.now() - (this.limits.rateLimitWindowMinutes * 60 * 1000);
    return attempts.filter(attempt => attempt.timestamp > cutoffTime);
  }

  /**
   * Check if email is currently locked out
   */
  isEmailLockedOut(email: string): { locked: boolean; remainingTime?: number } {
    const attempts = this.getStoredAttempts();
    const cleanAttempts = this.cleanOldAttempts(attempts);
    
    const emailAttempts = cleanAttempts.filter(
      attempt => attempt.email.toLowerCase() === email.toLowerCase() && !attempt.success
    );

    if (emailAttempts.length >= this.limits.maxAttemptsPerEmail) {
      const lastAttempt = Math.max(...emailAttempts.map(a => a.timestamp));
      const lockoutEnd = lastAttempt + (this.limits.lockoutDurationMinutes * 60 * 1000);
      const remainingTime = lockoutEnd - Date.now();
      
      if (remainingTime > 0) {
        return { 
          locked: true, 
          remainingTime: Math.ceil(remainingTime / 1000 / 60) // minutes
        };
      }
    }

    return { locked: false };
  }

  /**
   * Check if IP is currently rate limited
   */
  async isIPRateLimited(): Promise<{ limited: boolean; remainingTime?: number }> {
    const ip = await this.getUserIP();
    const attempts = this.getStoredAttempts();
    const cleanAttempts = this.cleanOldAttempts(attempts);
    
    const ipAttempts = cleanAttempts.filter(
      attempt => attempt.ip === ip && !attempt.success
    );

    if (ipAttempts.length >= this.limits.maxAttemptsPerIP) {
      const lastAttempt = Math.max(...ipAttempts.map(a => a.timestamp));
      const limitEnd = lastAttempt + (this.limits.rateLimitWindowMinutes * 60 * 1000);
      const remainingTime = limitEnd - Date.now();
      
      if (remainingTime > 0) {
        return { 
          limited: true, 
          remainingTime: Math.ceil(remainingTime / 1000 / 60) // minutes
        };
      }
    }

    return { limited: false };
  }

  /**
   * Record a login attempt (both localStorage and Supabase)
   */
  async recordLoginAttempt(email: string, success: boolean): Promise<void> {
    const ip = await this.getUserIP();

    // Store in localStorage for immediate access
    const attempts = this.getStoredAttempts();
    const cleanAttempts = this.cleanOldAttempts(attempts);

    const newAttempt: LoginAttempt = {
      email: email.toLowerCase(),
      timestamp: Date.now(),
      success,
      ip
    };

    cleanAttempts.push(newAttempt);
    this.storeAttempts(cleanAttempts);

    // Also store in Supabase for admin panel
    try {
      await supabase
        .from('login_attempts')
        .insert({
          email: email.toLowerCase(),
          ip_address: ip,
          success,
          user_agent: navigator.userAgent,
          metadata: {
            timestamp: Date.now(),
            url: window.location.href
          }
        });
    } catch (error) {
      console.error('Failed to store login attempt in Supabase:', error);
    }

    // If this is a failed attempt, check if we should create an admin notification
    if (!success) {
      await this.checkForSuspiciousActivity(email, cleanAttempts);

      // Also check for IP rate limiting
      const ipAttempts = cleanAttempts.filter(
        attempt => attempt.ip === ip && !attempt.success
      );

      console.log('🌐 IP rate limit check:', {
        ip,
        ipAttempts: ipAttempts.length,
        maxAllowed: this.limits.maxAttemptsPerIP,
        shouldLimit: ipAttempts.length >= this.limits.maxAttemptsPerIP
      });

      if (ipAttempts.length >= this.limits.maxAttemptsPerIP) {
        console.log('🚫 Rate limiting IP in database:', ip, 'attempts:', ipAttempts.length);
        await this.rateLimitIPInDatabase(ip, ipAttempts.length);
      }
    }
  }

  /**
   * Check for suspicious activity and create admin notifications
   */
  private async checkForSuspiciousActivity(email: string, attempts: LoginAttempt[]): Promise<void> {
    const emailAttempts = attempts.filter(
      attempt => attempt.email === email.toLowerCase() && !attempt.success
    );

    // Create notification after 3 failed attempts
    if (emailAttempts.length === 3) {
      await createSystemNotification(
        'Şüpheli Giriş Aktivitesi',
        `${email} hesabı için 3 başarısız giriş denemesi tespit edildi`,
        'warning'
      );
    }

    // Create critical notification and lock account after max attempts reached
    if (emailAttempts.length >= this.limits.maxAttemptsPerEmail) {
      console.log('🔒 Locking account in database:', email, 'attempts:', emailAttempts.length);

      await createSystemNotification(
        'Hesap Kilitlendi',
        `${email} hesabı brute force saldırısı nedeniyle kilitlendi`,
        'error'
      );

      // Lock account in database
      await this.lockAccountInDatabase(email, emailAttempts.length);
    }
  }

  /**
   * Lock account in database
   */
  private async lockAccountInDatabase(email: string, attemptCount: number): Promise<void> {
    try {
      const ip = await this.getUserIP();
      const lockedUntil = new Date(Date.now() + (this.limits.lockoutDurationMinutes * 60 * 1000));

      console.log('🔒 Attempting to lock account in database:', {
        email: email.toLowerCase(),
        lockedUntil: lockedUntil.toISOString(),
        attemptCount,
        ip
      });

      const { data, error } = await supabase
        .from('locked_accounts')
        .upsert({
          email: email.toLowerCase(),
          locked_until: lockedUntil.toISOString(),
          attempt_count: attemptCount,
          locked_by_ip: ip,
          is_active: true,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'email'
        });

      if (error) {
        console.error('❌ Failed to lock account in database:', error);
      } else {
        console.log('✅ Account locked in database successfully:', email, data);
      }
    } catch (error) {
      console.error('💥 Error locking account in database:', error);
    }
  }

  /**
   * Rate limit IP in database
   */
  private async rateLimitIPInDatabase(ip: string, attemptCount: number): Promise<void> {
    try {
      const limitedUntil = new Date(Date.now() + (this.limits.rateLimitWindowMinutes * 60 * 1000));

      console.log('🚫 Attempting to rate limit IP in database:', {
        ip,
        limitedUntil: limitedUntil.toISOString(),
        attemptCount
      });

      const { data, error } = await supabase
        .from('rate_limited_ips')
        .upsert({
          ip_address: ip,
          limited_until: limitedUntil.toISOString(),
          attempt_count: attemptCount,
          is_active: true,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'ip_address'
        });

      if (error) {
        console.error('❌ Failed to rate limit IP in database:', error);
      } else {
        console.log('✅ IP rate limited in database successfully:', ip, data);
      }
    } catch (error) {
      console.error('💥 Error rate limiting IP in database:', error);
    }
  }

  /**
   * Get remaining attempts for an email
   */
  getRemainingAttempts(email: string): number {
    const attempts = this.getStoredAttempts();
    const cleanAttempts = this.cleanOldAttempts(attempts);
    
    const emailAttempts = cleanAttempts.filter(
      attempt => attempt.email.toLowerCase() === email.toLowerCase() && !attempt.success
    );

    return Math.max(0, this.limits.maxAttemptsPerEmail - emailAttempts.length);
  }

  /**
   * Clear all stored attempts (for testing or admin reset)
   */
  clearAllAttempts(): void {
    localStorage.removeItem(this.storageKey);
  }

  /**
   * Get list of currently locked accounts
   */
  getLockedAccounts(): Array<{
    email: string;
    lockoutEnd: number;
    remainingMinutes: number;
    attemptCount: number;
  }> {
    const attempts = this.getStoredAttempts();
    const cleanAttempts = this.cleanOldAttempts(attempts);
    const lockedAccounts: Array<{
      email: string;
      lockoutEnd: number;
      remainingMinutes: number;
      attemptCount: number;
    }> = [];

    console.log('🔍 getLockedAccounts Debug:', {
      totalAttempts: attempts.length,
      cleanAttempts: cleanAttempts.length,
      maxAttemptsPerEmail: this.limits.maxAttemptsPerEmail,
      lockoutDurationMinutes: this.limits.lockoutDurationMinutes
    });

    // Group attempts by email
    const emailGroups = cleanAttempts.reduce((groups, attempt) => {
      const email = attempt.email.toLowerCase();
      if (!groups[email]) groups[email] = [];
      groups[email].push(attempt);
      return groups;
    }, {} as Record<string, LoginAttempt[]>);

    console.log('📧 Email groups:', emailGroups);

    // Check each email for lockout status
    Object.entries(emailGroups).forEach(([email, emailAttempts]) => {
      const failedAttempts = emailAttempts.filter(a => !a.success);

      console.log(`🔍 Checking ${email}:`, {
        totalAttempts: emailAttempts.length,
        failedAttempts: failedAttempts.length,
        isOverLimit: failedAttempts.length >= this.limits.maxAttemptsPerEmail
      });

      if (failedAttempts.length >= this.limits.maxAttemptsPerEmail) {
        const lastAttempt = Math.max(...failedAttempts.map(a => a.timestamp));
        const lockoutEnd = lastAttempt + (this.limits.lockoutDurationMinutes * 60 * 1000);
        const remainingTime = lockoutEnd - Date.now();

        console.log(`🔒 ${email} lockout check:`, {
          lastAttempt: new Date(lastAttempt).toISOString(),
          lockoutEnd: new Date(lockoutEnd).toISOString(),
          remainingTime,
          remainingMinutes: Math.ceil(remainingTime / 1000 / 60),
          isStillLocked: remainingTime > 0
        });

        if (remainingTime > 0) {
          lockedAccounts.push({
            email,
            lockoutEnd,
            remainingMinutes: Math.ceil(remainingTime / 1000 / 60),
            attemptCount: failedAttempts.length
          });
        }
      }
    });

    console.log('🔒 Final locked accounts:', lockedAccounts);
    return lockedAccounts.sort((a, b) => b.lockoutEnd - a.lockoutEnd);
  }

  /**
   * Unlock a specific account (admin function)
   */
  unlockAccount(email: string): void {
    const attempts = this.getStoredAttempts();
    // Remove all failed attempts for this email
    const filteredAttempts = attempts.filter(
      attempt => attempt.email.toLowerCase() !== email.toLowerCase() || attempt.success
    );
    this.storeAttempts(filteredAttempts);
  }

  /**
   * Get rate limited IPs
   */
  async getRateLimitedIPs(): Promise<Array<{
    ip: string;
    limitEnd: number;
    remainingMinutes: number;
    attemptCount: number;
  }>> {
    const attempts = this.getStoredAttempts();
    const cleanAttempts = this.cleanOldAttempts(attempts);
    const limitedIPs: Array<{
      ip: string;
      limitEnd: number;
      remainingMinutes: number;
      attemptCount: number;
    }> = [];

    // Group attempts by IP
    const ipGroups = cleanAttempts.reduce((groups, attempt) => {
      const ip = attempt.ip || 'unknown';
      if (!groups[ip]) groups[ip] = [];
      groups[ip].push(attempt);
      return groups;
    }, {} as Record<string, LoginAttempt[]>);

    // Check each IP for rate limit status
    Object.entries(ipGroups).forEach(([ip, ipAttempts]) => {
      const failedAttempts = ipAttempts.filter(a => !a.success);

      if (failedAttempts.length >= this.limits.maxAttemptsPerIP) {
        const lastAttempt = Math.max(...failedAttempts.map(a => a.timestamp));
        const limitEnd = lastAttempt + (this.limits.rateLimitWindowMinutes * 60 * 1000);
        const remainingTime = limitEnd - Date.now();

        if (remainingTime > 0) {
          limitedIPs.push({
            ip,
            limitEnd,
            remainingMinutes: Math.ceil(remainingTime / 1000 / 60),
            attemptCount: failedAttempts.length
          });
        }
      }
    });

    return limitedIPs.sort((a, b) => b.limitEnd - a.limitEnd);
  }

  /**
   * Remove IP rate limit (admin function)
   */
  removeIPLimit(ip: string): void {
    const attempts = this.getStoredAttempts();
    // Remove all failed attempts for this IP
    const filteredAttempts = attempts.filter(
      attempt => attempt.ip !== ip || attempt.success
    );
    this.storeAttempts(filteredAttempts);
  }

  /**
   * Get security statistics
   */
  getSecurityStats(): {
    totalAttempts: number;
    failedAttempts: number;
    uniqueEmails: number;
    uniqueIPs: number;
    rawAttempts: LoginAttempt[];
  } {
    const attempts = this.getStoredAttempts();
    const cleanAttempts = this.cleanOldAttempts(attempts);

    const failedAttempts = cleanAttempts.filter(a => !a.success);
    const uniqueEmails = new Set(cleanAttempts.map(a => a.email)).size;
    const uniqueIPs = new Set(cleanAttempts.map(a => a.ip)).size;

    console.log('🔍 Security Stats Debug:', {
      storedAttempts: attempts.length,
      cleanAttempts: cleanAttempts.length,
      failedAttempts: failedAttempts.length,
      rawData: attempts
    });

    return {
      totalAttempts: cleanAttempts.length,
      failedAttempts: failedAttempts.length,
      uniqueEmails,
      uniqueIPs,
      rawAttempts: cleanAttempts
    };
  }
}

export const securityHelper = new SecurityHelper();
export type { SecurityLimits, LoginAttempt };
